#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

function convertToNested(filePath) {
  console.log(`Converting ${filePath} to nested structure...`)
  
  // 读取原文件
  const content = fs.readFileSync(filePath, 'utf8')
  const flatObj = JSON.parse(content)
  
  const nestedObj = {}
  
  // 转换扁平结构到嵌套结构
  Object.keys(flatObj).forEach(key => {
    if (key.includes('.')) {
      const parts = key.split('.')
      const namespace = parts[0]
      const subKey = parts[1]
      
      if (!nestedObj[namespace]) {
        nestedObj[namespace] = {}
      }
      
      nestedObj[namespace][subKey] = flatObj[key]
    } else {
      // 如果已经是嵌套结构，直接复制
      nestedObj[key] = flatObj[key]
    }
  })
  
  // 写回文件
  const newContent = JSON.stringify(nestedObj, null, 2)
  fs.writeFileSync(filePath, newContent, 'utf8')
  
  console.log(`✅ Converted ${filePath}`)
  console.log(`   Keys: ${Object.keys(flatObj).length} -> ${Object.keys(nestedObj).length} namespaces`)
}

// 转换英文文件
const enFilePath = path.join(__dirname, '..', 'src', 'locales', 'en.json')
convertToNested(enFilePath)
