#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// 迁移规则
const migrationRules = [
  // Hook 导入迁移
  {
    pattern: /import\s*{\s*useTypedTranslation\s*}\s*from\s*['"]@\/hooks\/use-typed-translation['"]/g,
    replacement: "import { useTypedTranslation } from '@/hooks/use-typed-translation'"
  },
  
  // Hook 使用迁移 - 移除 namespace 参数
  {
    pattern: /const\s*{\s*([^}]+)\s*}\s*=\s*useTypedTranslation\s*\(\s*['"][^'"]*['"]\s*\)/g,
    replacement: 'const { $1 } = useTypedTranslation()'
  },
  
  // 翻译键迁移 - 从 namespace:key 到 namespace.key
  {
    pattern: /t\s*\(\s*['"]([^'"]+):([^'"]+)['"]\s*([^)]*)\)/g,
    replacement: "t('$1.$2'$3)"
  },
  
  // 常见的翻译键迁移
  {
    pattern: /t\s*\(\s*['"]create['"]\s*\)/g,
    replacement: "t('common.create')"
  },
  {
    pattern: /t\s*\(\s*['"]edit['"]\s*\)/g,
    replacement: "t('common.edit')"
  },
  {
    pattern: /t\s*\(\s*['"]delete['"]\s*\)/g,
    replacement: "t('common.delete')"
  },
  {
    pattern: /t\s*\(\s*['"]save['"]\s*\)/g,
    replacement: "t('common.save')"
  },
  {
    pattern: /t\s*\(\s*['"]cancel['"]\s*\)/g,
    replacement: "t('common.cancel')"
  },
  {
    pattern: /t\s*\(\s*['"]loading['"]\s*\)/g,
    replacement: "t('common.loading')"
  },
  {
    pattern: /t\s*\(\s*['"]search['"]\s*\)/g,
    replacement: "t('common.search')"
  }
]

// 获取所有 TypeScript/JavaScript 文件
function getAllFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = []
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        // 跳过 node_modules 和其他不需要的目录
        if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(item)) {
          traverse(fullPath)
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath)
      }
    }
  }
  
  traverse(dir)
  return files
}

// 应用迁移规则到文件
function migrateFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let hasChanges = false
    
    // 应用所有迁移规则
    for (const rule of migrationRules) {
      const originalContent = content
      content = content.replace(rule.pattern, rule.replacement)
      if (content !== originalContent) {
        hasChanges = true
      }
    }
    
    // 如果有变化，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8')
      return true
    }
    
    return false
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message)
    return false
  }
}

// 主迁移函数
function migrateI18nUsage() {
  console.log('🔄 Migrating i18n usage to new unified system...\n')
  
  const srcDir = path.join(__dirname, '..', 'src')
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found')
    process.exit(1)
  }
  
  const files = getAllFiles(srcDir)
  console.log(`📁 Found ${files.length} files to process\n`)
  
  let processedFiles = 0
  let modifiedFiles = 0
  
  for (const file of files) {
    const relativePath = path.relative(process.cwd(), file)
    
    try {
      const wasModified = migrateFile(file)
      processedFiles++
      
      if (wasModified) {
        modifiedFiles++
        console.log(`✅ Modified: ${relativePath}`)
      }
    } catch (error) {
      console.error(`❌ Failed to process ${relativePath}:`, error.message)
    }
  }
  
  console.log(`\n📊 Migration Summary:`)
  console.log(`   Files processed: ${processedFiles}`)
  console.log(`   Files modified: ${modifiedFiles}`)
  console.log(`   Files unchanged: ${processedFiles - modifiedFiles}`)
  
  if (modifiedFiles > 0) {
    console.log(`\n⚠️  Please review the changes and test your application!`)
    console.log(`   You may need to manually adjust some translations.`)
  } else {
    console.log(`\n✅ No files needed migration.`)
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
i18n Migration Tool

This tool helps migrate from the old namespace-based i18n system to the new unified system.

Usage:
  node scripts/migrate-i18n-usage.js [options]

Options:
  --help, -h    Show this help message
  --dry-run     Show what would be changed without making changes

Examples:
  node scripts/migrate-i18n-usage.js
  node scripts/migrate-i18n-usage.js --dry-run
`)
}

// 处理命令行参数
const args = process.argv.slice(2)

if (args.includes('--help') || args.includes('-h')) {
  showHelp()
  process.exit(0)
}

if (args.includes('--dry-run')) {
  console.log('🔍 Dry run mode - no files will be modified\n')
  // TODO: 实现 dry-run 模式
}

// 运行迁移
migrateI18nUsage()
