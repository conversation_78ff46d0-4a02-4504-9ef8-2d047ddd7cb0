'use client'

import { useEffect, useState, useMemo } from 'react'
import { useLocale } from './use-locale'
import type { SupportedLocale } from '@/types/i18n'

// 翻译缓存
const translationCache = new Map<string, any>()

export function useTypedTranslation(): {
  t: (key: string, options?: string | Record<string, any>) => string
  locale: SupportedLocale
  isLoading: boolean
} {
  const { locale } = useLocale()
  const [translations, setTranslations] = useState<Record<string, string> | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadTranslations = async () => {
      const cacheKey = locale

      // 检查缓存
      if (translationCache.has(cacheKey)) {
        setTranslations(translationCache.get(cacheKey))
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        const translationModule = await import(`@/locales/${locale}.json`)
        const translations = translationModule.default || translationModule

        // 缓存翻译结果
        translationCache.set(cacheKey, translations)
        setTranslations(translations)
      } catch (error) {
        console.error(`Failed to load translations for ${locale}:`, error)
        setTranslations(null)
      } finally {
        setIsLoading(false)
      }
    }

    loadTranslations()
  }, [locale])

  const t = (key: string, options?: string | Record<string, any>): string => {
    if (!translations) {
      if (typeof options === 'string') {
        return options
      }
      return key
    }

    // Handle nested keys like 'common.welcome'
    let translation: any = translations
    const keyParts = key.split('.')

    for (const part of keyParts) {
      if (translation && typeof translation === 'object' && part in translation) {
        translation = translation[part]
      } else {
        translation = undefined
        break
      }
    }

    if (!translation || typeof translation !== 'string') {
      console.warn(`Translation missing for key: ${key}`)
      if (typeof options === 'string') {
        return options
      }
      return key
    }

    // Handle interpolation
    if (typeof options === 'object' && options !== null) {
      Object.entries(options).forEach(([key, value]) => {
        const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
        translation = translation.replace(regex, String(value))
      })
    }

    return translation
  }

  return {
    t,
    locale: locale as SupportedLocale,
    isLoading
  }
}