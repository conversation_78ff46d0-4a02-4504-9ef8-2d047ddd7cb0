'use client'

import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { useClientTranslation } from '@/hooks/use-client-translation'

export function I18nExample() {
  const { t, locale, isLoading } = useTypedTranslation()
  
  if (isLoading) {
    return <div>Loading translations...</div>
  }

  return (
    <div className="p-4 space-y-4">
      <h1 className="text-2xl font-bold">
        {t('common.welcome')}
      </h1>

      <p className="text-gray-600">
        {t('common.introduction')}
      </p>

      <div className="space-y-2">
        <h2 className="text-lg font-semibold">
          {t('navbar.dashboard')}
        </h2>

        <nav className="space-x-4">
          <a href="#" className="text-blue-600 hover:underline">
            {t('navbar.config')}
          </a>
          <a href="#" className="text-blue-600 hover:underline">
            {t('navbar.channels')}
          </a>
          <a href="#" className="text-blue-600 hover:underline">
            {t('navbar.logs')}
          </a>
          <a href="#" className="text-blue-600 hover:underline">
            {t('navbar.users')}
          </a>
        </nav>
      </div>

      <div className="space-y-2">
        <h3 className="font-medium">
          {t('dashboard.statistics')}
        </h3>

        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 bg-gray-100 rounded">
            <div className="text-sm text-gray-600">
              {t('dashboard.totalUsers')}
            </div>
            <div className="text-xl font-bold">1,234</div>
          </div>

          <div className="p-3 bg-gray-100 rounded">
            <div className="text-sm text-gray-600">
              {t('dashboard.totalRequests')}
            </div>
            <div className="text-xl font-bold">
              {t('common.totalRecords', { count: 5678 })}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="font-medium">
          {t('common.actions')}
        </h3>

        <div className="space-x-2">
          <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            {t('common.create')}
          </button>
          <button className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
            {t('common.edit')}
          </button>
          <button className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
            {t('common.delete')}
          </button>
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-500">
        Current locale: {locale}
      </div>
    </div>
  )
}
