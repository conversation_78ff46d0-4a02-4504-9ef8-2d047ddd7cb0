# i18n 重构指南

## 概述

已将 i18n 系统重构为统一的翻译文件结构，使用点号分隔的键名格式，类似于：
```
"navbar.links.reverse": "Obrnuto dijeljenje",
"navbar.avatar.account": "<PERSON><PERSON> ra<PERSON><PERSON>",
"navbar.avatar.admin": "Administracija",
"navbar.avatar.signout": "Odjavi se"
```

## 文件结构

### 新结构
```
src/locales/
├── zh.json          # 中文翻译（统一文件）
├── en.json          # 英文翻译（统一文件）
└── [旧文件夹]       # 保留用于迁移参考
```

### 旧结构（已弃用）
```
src/locales/
├── zh/
│   ├── common.json
│   ├── dashboard.json
│   ├── config.json
│   └── ...
└── en/
    ├── common.json
    ├── dashboard.json
    ├── config.json
    └── ...
```

## 翻译键名格式

使用嵌套对象的层级结构：

```json
{
  "common": {
    "welcome": "欢迎使用 Rayin <PERSON>ush",
    "create": "创建",
    "edit": "编辑",
    "delete": "删除"
  },

  "navbar": {
    "dashboard": "仪表盘",
    "config": "接口配置",
    "channels": "通知渠道"
  },

  "dashboard": {
    "statistics": "统计信息",
    "totalUsers": "总用户数",
    "totalRequests": "总请求数"
  },

  "config": {
    "title": "接口配置",
    "createConfigDesc": "创建接口配置",
    "basicInfo": "基本信息"
  }
}
```

## 使用方法

### 1. 基础使用

```typescript
import { useTypedTranslation } from '@/hooks/use-typed-translation'

function MyComponent() {
  const { t, locale, isLoading } = useTypedTranslation()
  
  if (isLoading) {
    return <div>Loading...</div>
  }
  
  return (
    <div>
      <h1>{t('common.welcome')}</h1>
      <p>{t('common.introduction')}</p>
      <button>{t('common.create')}</button>
    </div>
  )
}
```

### 2. 带参数的翻译

```typescript
// 翻译文件中
{
  "common": {
    "totalRecords": "共 {{count}} 条记录",
    "selectedItems": "已选择 {{count}} 项"
  }
}

// 组件中使用
function MyComponent() {
  const { t } = useTypedTranslation()

  return (
    <div>
      <p>{t('common.totalRecords', { count: 100 })}</p>
      <p>{t('common.selectedItems', { count: 5 })}</p>
    </div>
  )
}
```

### 3. 客户端翻译（简化版）

```typescript
import { useClientTranslation } from '@/hooks/use-client-translation'

function SimpleComponent() {
  const { t, locale, isLoading } = useClientTranslation()
  
  return (
    <div>
      <h1>{t('common.welcome')}</h1>
      <p>Current locale: {locale}</p>
    </div>
  )
}
```

## 迁移指南

### 从旧系统迁移

**旧用法：**
```typescript
// 之前
const { t } = useTypedTranslation('common')
t('create')  // 或 t('common:create')

const { t: configT } = useTypedTranslation('config')
configT('title')
```

**新用法：**
```typescript
// 现在
const { t } = useTypedTranslation()
t('common.create')
t('config.title')
```

### 键名映射

| 旧格式 | 新格式 |
|--------|--------|
| `t('common:create')` | `t('common.create')` |
| `t('config:title')` | `t('config.title')` |
| `t('dashboard:statistics')` | `t('dashboard.statistics')` |
| `t('channels:channelName')` | `t('channels.channelName')` |

## 优势

1. **统一管理**: 所有翻译在一个文件中，便于管理和维护
2. **减少文件数量**: 从多个小文件合并为单个文件
3. **更好的IDE支持**: 点号分隔的键名提供更好的自动补全
4. **减少导入复杂性**: 不需要指定命名空间
5. **更清晰的层级结构**: 通过点号清晰表达翻译的层级关系

## 注意事项

1. **键名冲突**: 确保不同模块的键名不会冲突
2. **文件大小**: 单个文件可能较大，但现代打包工具会进行优化
3. **缓存**: 系统包含缓存机制，提高性能
4. **向后兼容**: 旧的hook仍然可用，但建议迁移到新系统

## 示例组件

参考 `src/components/i18n-example.tsx` 查看完整的使用示例。
