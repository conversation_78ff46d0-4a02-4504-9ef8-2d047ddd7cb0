'use client'

import { use<PERSON>ara<PERSON>, useRouter, usePathname } from 'next/navigation'
import { useTranslation } from 'react-i18next'

export function useLocale() {
  const params = useParams()
  const router = useRouter()
  const pathname = usePathname()
  
  const currentLocale = params?.locale as string || 'zh'
  
  const switchLocale = (newLocale: string) => {
    // 替换当前路径中的语言代码
    const segments = pathname.split('/')
    segments[1] = newLocale
    const newPath = segments.join('/')
    
    router.push(newPath)
  }
  
  return {
    locale: currentLocale,
    switchLocale,
    isZh: currentLocale === 'zh',
    isEn: currentLocale === 'en'
  }
}

export function useI18n() {
  const { t, i18n } = useTranslation()
  const { locale, switchLocale } = useLocale()

  return {
    t,
    locale,
    switchLocale,
    i18n
  }
}