'use client'

import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { Card } from '@/components/ui/card'

export default function TestI18nPage() {
  const { t, locale, isLoading } = useTypedTranslation()
  
  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-4">
          i18n 嵌套结构测试页面
        </h1>
        
        <div className="space-y-4">
          <div>
            <h2 className="text-lg font-semibold mb-2">Common 翻译测试</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>welcome:</strong> {t('common.welcome')}
              </div>
              <div>
                <strong>introduction:</strong> {t('common.introduction')}
              </div>
              <div>
                <strong>create:</strong> {t('common.create')}
              </div>
              <div>
                <strong>edit:</strong> {t('common.edit')}
              </div>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">Navbar 翻译测试</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>dashboard:</strong> {t('navbar.dashboard')}
              </div>
              <div>
                <strong>config:</strong> {t('navbar.config')}
              </div>
              <div>
                <strong>channels:</strong> {t('navbar.channels')}
              </div>
              <div>
                <strong>users:</strong> {t('navbar.users')}
              </div>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">Dashboard 翻译测试</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>statistics:</strong> {t('dashboard.statistics')}
              </div>
              <div>
                <strong>totalUsers:</strong> {t('dashboard.totalUsers')}
              </div>
              <div>
                <strong>totalRequests:</strong> {t('dashboard.totalRequests')}
              </div>
              <div>
                <strong>successRate:</strong> {t('dashboard.successRate')}
              </div>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">带参数的翻译测试</h2>
            <div className="space-y-2">
              <div>
                <strong>totalRecords (count=100):</strong> {t('common.totalRecords', { count: 100 })}
              </div>
              <div>
                <strong>selectedItems (count=5):</strong> {t('common.selectedItems', { count: 5 })}
              </div>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-2">错误处理测试</h2>
            <div className="space-y-2">
              <div>
                <strong>不存在的键:</strong> {t('nonexistent.key')}
              </div>
              <div>
                <strong>部分存在的键:</strong> {t('common.nonexistent')}
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-gray-100 rounded">
            <p><strong>当前语言:</strong> {locale}</p>
            <p><strong>加载状态:</strong> {isLoading ? '加载中' : '已加载'}</p>
          </div>
        </div>
      </Card>
    </div>
  )
}
