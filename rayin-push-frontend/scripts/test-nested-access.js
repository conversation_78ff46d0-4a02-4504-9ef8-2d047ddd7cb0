#!/usr/bin/env node

// 测试嵌套键访问
const translations = {
  common: {
    welcome: '欢迎使用 Rayin Push',
    create: '创建'
  },
  dashboard: {
    statistics: '统计信息'
  }
};

function t(key) {
  let translation = translations;
  const keyParts = key.split('.');
  
  for (const part of keyParts) {
    if (translation && typeof translation === 'object' && part in translation) {
      translation = translation[part];
    } else {
      translation = undefined;
      break;
    }
  }
  
  if (translation && typeof translation === 'string') {
    return translation;
  }
  
  return key;
}

console.log('Testing nested key access:');
console.log('t("common.welcome"):', t('common.welcome'));
console.log('t("common.create"):', t('common.create'));
console.log('t("dashboard.statistics"):', t('dashboard.statistics'));
console.log('t("nonexistent.key"):', t('nonexistent.key'));
console.log('t("common"):', t('common')); // Should return key, not object
