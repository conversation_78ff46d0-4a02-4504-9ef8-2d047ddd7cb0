#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// 读取翻译文件
function loadTranslations(locale) {
  const filePath = path.join(__dirname, '..', 'src', 'locales', `${locale}.json`)
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    return JSON.parse(content)
  } catch (error) {
    console.error(`❌ Failed to load ${locale}.json:`, error.message)
    return null
  }
}

// 验证翻译完整性
function validateTranslations() {
  console.log('🔍 Validating i18n translations...\n')
  
  const zhTranslations = loadTranslations('zh')
  const enTranslations = loadTranslations('en')
  
  if (!zhTranslations || !enTranslations) {
    console.error('❌ Failed to load translation files')
    process.exit(1)
  }
  
  const zhKeys = Object.keys(zhTranslations).sort()
  const enKeys = Object.keys(enTranslations).sort()
  
  console.log(`📊 Translation statistics:`)
  console.log(`   Chinese (zh): ${zhKeys.length} keys`)
  console.log(`   English (en): ${enKeys.length} keys\n`)
  
  // 检查键的一致性
  const missingInEn = zhKeys.filter(key => !enKeys.includes(key))
  const missingInZh = enKeys.filter(key => !zhKeys.includes(key))
  
  if (missingInEn.length > 0) {
    console.error('❌ Keys missing in English translation:')
    missingInEn.forEach(key => console.error(`   - ${key}`))
    console.log()
  }
  
  if (missingInZh.length > 0) {
    console.error('❌ Keys missing in Chinese translation:')
    missingInZh.forEach(key => console.error(`   - ${key}`))
    console.log()
  }
  
  // 检查空值
  const emptyZhValues = zhKeys.filter(key => !zhTranslations[key] || zhTranslations[key].trim() === '')
  const emptyEnValues = enKeys.filter(key => !enTranslations[key] || enTranslations[key].trim() === '')
  
  if (emptyZhValues.length > 0) {
    console.error('❌ Empty values in Chinese translation:')
    emptyZhValues.forEach(key => console.error(`   - ${key}`))
    console.log()
  }
  
  if (emptyEnValues.length > 0) {
    console.error('❌ Empty values in English translation:')
    emptyEnValues.forEach(key => console.error(`   - ${key}`))
    console.log()
  }
  
  // 检查插值变量的一致性
  const interpolationRegex = /\{\{(\w+)\}\}/g
  const inconsistentInterpolations = []
  
  zhKeys.forEach(key => {
    if (enKeys.includes(key)) {
      const zhMatches = (zhTranslations[key].match(interpolationRegex) || []).sort()
      const enMatches = (enTranslations[key].match(interpolationRegex) || []).sort()
      
      if (JSON.stringify(zhMatches) !== JSON.stringify(enMatches)) {
        inconsistentInterpolations.push({
          key,
          zh: zhMatches,
          en: enMatches
        })
      }
    }
  })
  
  if (inconsistentInterpolations.length > 0) {
    console.error('❌ Inconsistent interpolation variables:')
    inconsistentInterpolations.forEach(({ key, zh, en }) => {
      console.error(`   - ${key}:`)
      console.error(`     zh: ${zh.join(', ') || 'none'}`)
      console.error(`     en: ${en.join(', ') || 'none'}`)
    })
    console.log()
  }
  
  // 检查键名格式
  const invalidKeys = [...zhKeys, ...enKeys].filter(key => {
    // 检查是否符合点号分隔的格式
    return !/^[a-zA-Z][a-zA-Z0-9]*(\.[a-zA-Z][a-zA-Z0-9]*)+$/.test(key)
  })
  
  if (invalidKeys.length > 0) {
    console.error('❌ Invalid key format (should be dot-separated):')
    const uniqueInvalidKeys = Array.from(new Set(invalidKeys))
    uniqueInvalidKeys.forEach(key => console.error(`   - ${key}`))
    console.log()
  }
  
  // 总结
  const hasErrors = missingInEn.length > 0 || missingInZh.length > 0 || 
                   emptyZhValues.length > 0 || emptyEnValues.length > 0 ||
                   inconsistentInterpolations.length > 0 || invalidKeys.length > 0
  
  if (hasErrors) {
    console.error('❌ Validation failed! Please fix the issues above.')
    process.exit(1)
  } else {
    console.log('✅ All translations are valid!')
    console.log('✅ Key consistency: OK')
    console.log('✅ No empty values: OK')
    console.log('✅ Interpolation consistency: OK')
    console.log('✅ Key format: OK')
  }
}

// 运行验证
validateTranslations()
