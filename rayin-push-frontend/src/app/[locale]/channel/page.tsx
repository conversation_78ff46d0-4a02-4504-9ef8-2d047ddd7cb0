'use client'

import { AppLayout } from '@/components/app-layout'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { useStoreInitialization } from '@/hooks/use-store'
import { ChannelList } from '@/components/channel/channel-list'
import { ChannelToolbar } from '@/components/channel/channel-toolbar'
import { ChannelEditModal } from '@/components/channel/channel-form'
import { useState } from 'react'
import { mockNotificationChannels } from '@/mock/channel'
import type { NotificationChannel } from '@/types/data'

export default function ChannelPage() {
  const { t, isLoading } = useTypedTranslation()
  const { isHydrated } = useStoreInitialization()
  const [channel, setChannel] = useState<NotificationChannel[]>(mockNotificationChannels)
  const [selectedChannel, setSelectedChannel] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  const [typeFilter, setTypeFilter] = useState<'all' | 'wechat' | 'feishu' | 'webhook'>('all')
  const [createModalOpen, setCreateModalOpen] = useState(false)

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // 过滤渠道数据
  const filteredChannel = channel.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter
    const matchesType = typeFilter === 'all' || item.type === typeFilter
    return matchesSearch && matchesStatus && matchesType
  })

  // 分页数据
  const total = filteredChannel.length
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedChannel = filteredChannel.slice(startIndex, endIndex)

  // 处理渠道状态切换
  const handleToggleStatus = (id: string) => {
    setChannel(prev => prev.map(item =>
      item.id === id
        ? { ...item, status: item.status === 'active' ? 'inactive' : 'active' }
        : item
    ))
  }

  // 处理更新渠道
  const handleUpdateChannel = (updatedChannel: NotificationChannel) => {
    setChannel(prev => prev.map(item =>
      item.id === updatedChannel.id ? updatedChannel : item
    ))
  }

  // 处理删除渠道
  const handleDeleteChannel = (id: string) => {
    setChannel(prev => prev.filter(item => item.id !== id))
    setSelectedChannel(prev => prev.filter(channelId => channelId !== id))
  }

  // 处理创建渠道
  const handleCreateChannel = (newChannel: NotificationChannel) => {
    const channelWithId = {
      ...newChannel,
      id: Date.now().toString(),
      createdTime: new Date().toISOString(),
      updatedTime: new Date().toISOString(),
      creator: 'current_user' // 这里应该从用户状态获取
    }
    setChannel(prev => [...prev, channelWithId])
    setCreateModalOpen(false)
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    setChannel(prev => prev.filter(item => !selectedChannel.includes(item.id)))
    setSelectedChannel([])
  }

  // 处理批量状态切换
  const handleBatchToggleStatus = (status: 'active' | 'inactive') => {
    setChannel(prev => prev.map(item =>
      selectedChannel.includes(item.id)
        ? { ...item, status }
        : item
    ))
    setSelectedChannel([])
  }

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    setSelectedChannel([]) // 切换页面时清空选择
  }

  // 处理每页数量变化
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize)
    setCurrentPage(1) // 重置到第一页
    setSelectedChannel([]) // 清空选择
  }

  // 当搜索或筛选条件变化时重置到第一页
  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
    setSelectedChannel([])
  }

  const handleStatusFilterChange = (status: 'all' | 'active' | 'inactive') => {
    setStatusFilter(status)
    setCurrentPage(1)
    setSelectedChannel([])
  }

  const handleTypeFilterChange = (type: 'all' | 'wechat' | 'feishu' | 'webhook') => {
    setTypeFilter(type)
    setCurrentPage(1)
    setSelectedChannel([])
  }

  // 只有在翻译加载中或应用未水合时显示加载状态
  if (isLoading || !isHydrated) {
    return (
      <AppLayout title={t('channel.title')}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout title={t('channel.title')}>
      <div className="p-4 space-y-4">
        {/* 工具栏 */}
        <ChannelToolbar
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          statusFilter={statusFilter}
          onStatusFilterChange={handleStatusFilterChange}
          typeFilter={typeFilter}
          onTypeFilterChange={handleTypeFilterChange}
          onCreateChannel={() => setCreateModalOpen(true)}
        />

        {/* 渠道列表 */}
        <ChannelList
          channels={paginatedChannel}
          selectedChannels={selectedChannel}
          onSelectionChange={setSelectedChannel}
          onToggleStatus={handleToggleStatus}
          onDeleteChannel={handleDeleteChannel}
          onUpdateChannel={handleUpdateChannel}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          onBatchDelete={handleBatchDelete}
          onBatchToggleStatus={handleBatchToggleStatus}
        />

        {/* 创建渠道模态框 */}
        <ChannelEditModal
          channel={null}
          open={createModalOpen}
          onOpenChange={setCreateModalOpen}
          onSave={handleCreateChannel}
        />
      </div>
    </AppLayout>
  )
}