'use client'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, Plus } from 'lucide-react'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ChannelToolbarProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: 'all' | 'active' | 'inactive'
  onStatusFilterChange: (status: 'all' | 'active' | 'inactive') => void
  typeFilter: 'all' | 'wechat' | 'feishu' | 'webhook'
  onTypeFilterChange: (type: 'all' | 'wechat' | 'feishu' | 'webhook') => void
  onCreateChannel: () => void
}

export function ChannelToolbar({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  typeFilter,
  onTypeFilterChange,
  onCreateChannel
}: ChannelToolbarProps) {
  const { t } = useTypedTranslation()
  const { t: tCommon } = useTypedTranslation('common')

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
      {/* 左侧搜索和筛选 */}
      <div className="flex flex-col sm:flex-row gap-3 flex-1 max-w-2xl">
        {/* 搜索框 */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('channel.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* 状态筛选 */}
        <Select value={statusFilter} onValueChange={onStatusFilterChange}>
          <SelectTrigger className="w-[140px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{tCommon('allStatus')}</SelectItem>
            <SelectItem value="active">{tCommon('active')}</SelectItem>
            <SelectItem value="inactive">{tCommon('inactive')}</SelectItem>
          </SelectContent>
        </Select>

        {/* 类型筛选 */}
        <Select value={typeFilter} onValueChange={onTypeFilterChange}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{tCommon('allTypes')}</SelectItem>
            <SelectItem value="wechat">{t('wechatBot')}</SelectItem>
            <SelectItem value="feishu">{t('feishuBot')}</SelectItem>
            <SelectItem value="webhook">{t('customWebhook')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 右侧操作按钮 */}
      <div className="flex gap-2 items-center">
        {/* 创建渠道按钮 */}
        <Button onClick={onCreateChannel} className="cursor-pointer">
          <Plus className="h-4 w-4 mr-1" />
          {t('channel.createChannel')}
        </Button>
      </div>
    </div>
  )
}