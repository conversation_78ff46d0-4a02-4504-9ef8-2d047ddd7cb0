# i18N 重构总结

## 修改概述

根据要求，对i18N文件进行了重构，将操作相关的文本统一展示为启用/禁用，并将通用操作（编辑、删除、创建等）移到common文件中，避免重复定义。

## 主要修改

### 1. 移除重复的操作定义

从各个模块的翻译文件中移除了以下重复定义，统一使用common.json中的定义：

#### 英文版本 (en/)
- **channels.json**: 移除 `createChannel`, `editChannel`, `deleteChannel`
- **config.json**: 移除 `createConfig`, `editConfig`, `deleteConfig`, `enableConfig`, `disableConfig`, `enabled`, `disabled`, `cancel`, `save`, `allStatus`
- **limits.json**: 移除 `createRule`, `editRule`, `deleteRule`, `enableRule`, `disableRule`
- **users.json**: 移除 `createUser`, `editUser`, `deleteUser`, `enableUser`, `disableUser`, `active`, `inactive`, `allStatus`
- **logs.json**: 移除 `allStatus`

#### 中文版本 (zh/)
- **channels.json**: 移除 `createChannel`, `editChannel`, `deleteChannel`
- **config.json**: 移除 `createConfig`, `editConfig`, `deleteConfig`, `enableConfig`, `disableConfig`, `enabled`, `disabled`, `cancel`, `save`, `allStatus`
- **limits.json**: 移除 `createRule`, `editRule`, `deleteRule`, `enableRule`, `disableRule`
- **users.json**: 移除 `createUser`, `editUser`, `deleteUser`, `enableUser`, `disableUser`, `active`, `inactive`, `allStatus`
- **logs.json**: 移除 `allStatus`

### 2. 统一的操作文本

现在所有模块都使用common.json中的统一定义：

#### 通用操作
- `create`: "Create" / "创建"
- `edit`: "Edit" / "编辑"  
- `delete`: "Delete" / "删除"
- `save`: "Save" / "保存"
- `cancel`: "Cancel" / "取消"

#### 状态操作（统一为启用/禁用）
- `enable`: "Enable" / "启用"
- `disable`: "Disable" / "禁用"
- `active`: "Active" / "启用"
- `inactive`: "Inactive" / "禁用"

#### 筛选选项
- `allStatus`: "All Status" / "全部状态"
- `allTypes`: "All Types" / "全部类型"

### 3. TypeScript类型定义更新

更新了 `src/types/i18n.ts` 文件，移除了已删除字段的类型定义：

- `ChannelsTranslations`: 移除 `createChannel`, `editChannel`, `deleteChannel`
- `ConfigTranslations`: 移除 `createConfig`, `editConfig`, `deleteConfig`, `enableConfig`, `disableConfig`, `enabled`, `disabled`, `cancel`, `save`, `allStatus`
- `LimitsTranslations`: 移除 `createRule`, `editRule`, `deleteRule`, `enableRule`, `disableRule`
- `UsersTranslations`: 移除 `createUser`, `editUser`, `deleteUser`, `enableUser`, `disableUser`, `active`, `inactive`, `allStatus`
- `LogsTranslations`: 移除 `allStatus`

## 验证结果

运行翻译验证脚本 `scripts/validate-translations.js`，确认：
- ✅ 所有翻译文件格式正确
- ✅ 中英文对应的键值完整
- ✅ 没有缺失或空值

## 使用方式

现在在组件中使用操作相关的文本时，应该：

```typescript
// 之前
t('channels:createChannel')  // ❌ 已移除
t('channels:editChannel')    // ❌ 已移除
t('channels:deleteChannel')  // ❌ 已移除

// 现在
t('common:create')           // ✅ 统一使用
t('common:edit')             // ✅ 统一使用
t('common:delete')           // ✅ 统一使用
t('common:enable')           // ✅ 统一使用
t('common:disable')          // ✅ 统一使用
t('common:active')           // ✅ 统一使用
t('common:inactive')         // ✅ 统一使用
t('common:allStatus')        // ✅ 统一使用
```

## 优势

1. **减少重复**: 避免了在多个文件中维护相同的翻译文本
2. **统一性**: 确保所有模块的操作文本保持一致
3. **维护性**: 只需在common.json中修改一次，所有地方都会生效
4. **启用/禁用统一**: 所有状态操作都统一显示为启用/禁用，提供一致的用户体验
