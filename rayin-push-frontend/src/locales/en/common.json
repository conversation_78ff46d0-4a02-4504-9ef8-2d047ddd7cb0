{"welcome": "Welcome to <PERSON><PERSON>", "introduction": "Customizable Webhook message parsing and forwarding system", "description": "Description", "dashboard": "Dashboard", "config": "Interface Config", "channels": "Notification Channels", "logs": "Request Logs", "limits": "Request Limits", "users": "User Management", "settings": "Settings", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "search": "Search", "create": "Create", "edit": "Edit", "delete": "Delete", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "failed": "Failed", "status": "Status", "allStatus": "All Status", "actions": "Actions", "name": "Name", "time": "Time", "type": "Type", "allTypes": "All Types", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "disabled": "Disabled", "token": "Token", "requestMethod": "Request Method", "management": "Management", "system": "System", "monitoring": "System Monitoring", "analytics": "Analytics", "webhooks": "Webhook Management", "database": "Database Status", "operate": "Operate", "createdTime": "Created Time", "updatedTime": "Updated Time", "totalRecords": "Total {{count}} records", "selectedItems": "{{count}} selected", "batchOperation": "Batch Operation", "batchEnable": "Batch Enable", "batchDisable": "<PERSON>ch Disable", "perPage": "Per page", "itemsPerPage": "items", "firstPage": "First", "lastPage": "Last", "previousPage": "Previous", "nextPage": "Next", "page": "Page {{current}} of {{total}}", "noData": "No data", "auto": "Auto", "showing": "Showing {{from}} to {{to}} of {{total}} entries", "rowsPerPage": "Rows per page"}