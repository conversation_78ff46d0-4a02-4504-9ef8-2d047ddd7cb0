'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Power, 
  PowerOff, 
  Copy,
  CheckCheck,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react'
import { useTypedTranslation } from '@/hooks/use-typed-translation'
import { ConfigEditModal } from './config-form'
import type { InterfaceConfig } from '@/types/data'
import { format } from 'date-fns'

interface ConfigListProps {
  configs: InterfaceConfig[]
  selectedConfigs: string[]
  onSelectionChange: (selected: string[]) => void
  onToggleStatus: (id: string) => void
  onDeleteConfig: (id: string) => void
  onUpdateConfig: (config: InterfaceConfig) => void
  // 分页相关
  currentPage?: number
  pageSize?: number
  total?: number
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void
  // 批量操作
  onBatchDelete?: () => void
  onBatchToggleStatus?: (status: 'enabled' | 'disabled') => void
}

export function ConfigList({
  configs,
  selectedConfigs,
  onSelectionChange,
  onToggleStatus,
  onDeleteConfig,
  onUpdateConfig,
  currentPage = 1,
  pageSize = 10,
  total = 0,
  onPageChange,
  onPageSizeChange,
  onBatchDelete,
  onBatchToggleStatus
}: ConfigListProps) {
  const { t } = useTypedTranslation('config')
  const { t: tCommon } = useTypedTranslation('common')
  
  // 编辑模态框状态
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editingConfig, setEditingConfig] = useState<InterfaceConfig | null>(null)
  
  // 复制状态
  const [copiedTokens, setCopiedTokens] = useState<Set<string>>(new Set())

  // 处理编辑配置
  const handleEditConfig = (config: InterfaceConfig) => {
    setEditingConfig(config)
    setEditModalOpen(true)
  }

  // 处理保存配置
  const handleSaveConfig = (config: InterfaceConfig) => {
    onUpdateConfig(config)
    setEditModalOpen(false)
    setEditingConfig(null)
  }

  // 处理复制 Token
  const handleCopyToken = async (token: string) => {
    try {
      await navigator.clipboard.writeText(token)
      setCopiedTokens(prev => new Set(prev).add(token))
      
      // 2秒后清除复制状态
      setTimeout(() => {
        setCopiedTokens(prev => {
          const newSet = new Set(prev)
          newSet.delete(token)
          return newSet
        })
      }, 2000)
    } catch (error) {
      console.error('Failed to copy token:', error)
    }
  }

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(configs.map(config => config.id))
    } else {
      onSelectionChange([])
    }
  }

  // 处理单项选择
  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedConfigs, id])
    } else {
      onSelectionChange(selectedConfigs.filter(configId => configId !== id))
    }
  }

  // 格式化 Token 显示（只显示头尾，中间用*代替）
  const formatTokenDisplay = (token: string) => {
    if (token.length <= 16) return token
    return `${token.slice(0, 6)}********************${token.slice(-4)}`
  }

  // 格式化日期为 yyyy-MM-dd HH:mm:ss
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss')
  }

  // 获取方法颜色
  const getMethodColor = (method: string) => {
    const colors = {
      'GET': 'bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800',
      'POST': 'bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800',
      'PUT': 'bg-orange-50 text-orange-700 border border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800',
      'DELETE': 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
      'PATCH': 'bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800'
    }
    return colors[method as keyof typeof colors] || 'bg-gray-50 text-gray-700 border border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600'
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    return status === 'enabled' 
      ? 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800' 
      : 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800'
  }

  const isAllSelected = configs.length > 0 && selectedConfigs.length === configs.length

  if (configs.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground">
          {t('noConfigData')}
        </div>
      </div>
    )
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-8 pl-4">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead className="w-48">{tCommon('name')}</TableHead>
            <TableHead className="w-56">{tCommon('token')}</TableHead>
            <TableHead className="w-64">{tCommon('description')}</TableHead>
            <TableHead className="w-20">{tCommon('requestMethod')}</TableHead>
            <TableHead className="w-20">{tCommon('status')}</TableHead>
            <TableHead className="w-40">{tCommon('createdTime')}</TableHead>
            <TableHead className="w-40">{tCommon('updatedTime')}</TableHead>
            <TableHead className="w-16">{tCommon('operate')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {configs.map((config) => (
            <TableRow key={config.id}>
              <TableCell className="w-8 pl-4">
                <Checkbox
                  checked={selectedConfigs.includes(config.id)}
                  onCheckedChange={(checked) => 
                    handleSelectItem(config.id, checked as boolean)
                  }
                />
              </TableCell>
              <TableCell className="font-medium w-48 max-w-48">
                <div className="truncate" title={config.name}>
                  {config.name}
                </div>
              </TableCell>
              <TableCell className="font-mono text-xs px-2 w-56 max-w-56">
                <div className="flex items-center gap-2">
                  <div className="truncate">
                    {formatTokenDisplay(config.token)}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-muted cursor-pointer"
                    onClick={() => handleCopyToken(config.token)}
                    title="复制 Token"
                  >
                    {copiedTokens.has(config.token) ? (
                      <CheckCheck className="h-3 w-3 text-green-600" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                </div>
              </TableCell>
              <TableCell className="w-64 max-w-64">
                <div className="truncate" title={config.description}>
                  {config.description}
                </div>
              </TableCell>
              <TableCell className="pl-2 w-20">
                <div className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium ${getMethodColor(config.method)}`}>
                  {config.method}
                </div>
              </TableCell>
              <TableCell className="w-20">
                <div className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium ${getStatusColor(config.status)}`}>
                  {config.status === 'enabled' ? tCommon('enabled') : tCommon('disabled')}
                </div>
              </TableCell>
              <TableCell className="w-40">
                <div className="text-sm text-muted-foreground">
                  {formatDate(config.createdTime)}
                </div>
              </TableCell>
              <TableCell className="w-40">
                <div className="text-sm text-muted-foreground">
                  {formatDate(config.updatedTime)}
                </div>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 cursor-pointer focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-transparent focus:ring-0 focus:ring-offset-0 focus:border-transparent">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem 
                      onClick={() => handleEditConfig(config)}
                      className="cursor-pointer"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      {tCommon('edit')}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onToggleStatus(config.id)}
                      className="cursor-pointer"
                    >
                      {config.status === 'enabled' ? (
                        <>
                          <PowerOff className="h-4 w-4 mr-2" />
                          {tCommon('disable')}
                        </>
                      ) : (
                        <>
                          <Power className="h-4 w-4 mr-2" />
                          {tCommon('enable')}
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-destructive hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer"
                      onClick={() => onDeleteConfig(config.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                      {tCommon('delete')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* 分页组件 */}
      {onPageChange && total > 0 && (
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-6 py-4 border-t bg-gray-50/50 dark:bg-gray-900/50 dark:border-gray-800">
          {/* 左侧：数据统计信息和批量操作 */}
          <div className="flex items-center gap-4 text-sm">
            <span className="text-muted-foreground">{tCommon('totalRecords', { count: total })}</span>
            {selectedConfigs.length > 0 && (
              <>
                <span className="text-blue-600 font-medium">
                  {tCommon('selectedItems', { count: selectedConfigs.length })}
                </span>
                
                {/* 批量操作按钮 */}
                <div className="flex items-center gap-2">
                  {onBatchToggleStatus && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="cursor-pointer h-8">
                          <Power className="h-4 w-4 mr-1" />
                          {tCommon('batchOperation')}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem 
                          onClick={() => onBatchToggleStatus('enabled')} 
                          className="cursor-pointer"
                        >
                          <Power className="h-4 w-4 mr-2" />
                          {tCommon('batchEnable')}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => onBatchToggleStatus('disabled')} 
                          className="cursor-pointer"
                        >
                          <PowerOff className="h-4 w-4 mr-2" />
                          {tCommon('batchDisable')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                  
                  {onBatchDelete && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={onBatchDelete}
                      className="cursor-pointer h-8"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      {tCommon('delete')}
                    </Button>
                  )}
                </div>
              </>
            )}
          </div>
          
          {/* 右侧：分页控件 */}
          <div className="flex items-center gap-4">
            {/* 每页显示数量 */}
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">{tCommon('perPage')}</span>
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => onPageSizeChange?.(Number(value))}
              >
                <SelectTrigger className="w-20 h-8 text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-muted-foreground">{tCommon('itemsPerPage')}</span>
            </div>
            
            {/* 页码信息和导航 */}
            <div className="flex items-center gap-1">
              {/* 最前页按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(1)}
                disabled={currentPage <= 1}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              
              {/* 上一页按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage <= 1}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              {/* 页码按钮组 */}
              <div className="flex items-center gap-1 mx-2">
                {(() => {
                  const totalPages = Math.ceil(total / pageSize)
                  const pages = []
                  
                  // 计算显示的页码范围
                  let startPage = Math.max(1, currentPage - 2)
                  let endPage = Math.min(totalPages, currentPage + 2)
                  
                  // 如果当前页靠近开始，显示更多后面的页码
                  if (currentPage <= 3) {
                    endPage = Math.min(totalPages, 5)
                  }
                  
                  // 如果当前页靠近结束，显示更多前面的页码
                  if (currentPage > totalPages - 3) {
                    startPage = Math.max(1, totalPages - 4)
                  }
                  
                  // 第一页
                  if (startPage > 1) {
                    pages.push(
                      <Button
                        key={1}
                        variant="outline"
                        size="sm"
                        onClick={() => onPageChange(1)}
                        className="h-8 w-8 p-0 cursor-pointer"
                      >
                        1
                      </Button>
                    )
                    if (startPage > 2) {
                      pages.push(
                        <span key="ellipsis1" className="px-2 text-muted-foreground">
                          ...
                        </span>
                      )
                    }
                  }
                  
                  // 中间页码
                  for (let i = startPage; i <= endPage; i++) {
                    pages.push(
                      <Button
                        key={i}
                        variant={i === currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => onPageChange(i)}
                        className="h-8 w-8 p-0 cursor-pointer"
                      >
                        {i}
                      </Button>
                    )
                  }
                  
                  // 最后一页
                  if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                      pages.push(
                        <span key="ellipsis2" className="px-2 text-muted-foreground">
                          ...
                        </span>
                      )
                    }
                    pages.push(
                      <Button
                        key={totalPages}
                        variant="outline"
                        size="sm"
                        onClick={() => onPageChange(totalPages)}
                        className="h-8 w-8 p-0 cursor-pointer"
                      >
                        {totalPages}
                      </Button>
                    )
                  }
                  
                  return pages
                })()}
              </div>
              
              {/* 下一页按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage >= Math.ceil(total / pageSize)}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              
              {/* 最后页按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(Math.ceil(total / pageSize))}
                disabled={currentPage >= Math.ceil(total / pageSize)}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑配置模态框 */}
      <ConfigEditModal
        config={editingConfig}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onSave={handleSaveConfig}
      />
    </div>
  )
}