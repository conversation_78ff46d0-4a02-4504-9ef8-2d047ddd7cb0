import type { NotificationChannel } from '@/types/data'

export const mockNotificationChannels: NotificationChannel[] = [
  {
    id: '1',
    name: '开发团队微信群11111111111111111111111111111111111111111111111111',
    type: 'wechat',
    description: '开发团队日常通知群22222222222222222222222222222222222222222222222222222222222222222222222222222222222',
    status: 'active',
    config: {
      url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx-dev-team',
      template: '**${title}**\n> ${content}\n> 时间: ${now}\n> 数据: ${data}'
    },
    createdTime: '2024-01-10T09:00:00Z',
    updatedTime: '2024-12-20T15:30:00Z',
    lastTestAt: '2024-12-25T10:00:00Z',
    testStatus: 'success',
    creator: 'admin'
  },
  {
    id: '2',
    name: '运维告警飞书群',
    type: 'feishu',
    description: '系统运维告警通知',
    status: 'active',
    config: {
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/xxx-ops-alert',
      template: '**🚨 ${level} 告警**\n**服务**: ${service}\n**消息**: ${message}\n**时间**: ${timestamp}'
    },
    createdTime: '2024-01-15T10:30:00Z',
    updatedTime: '2024-12-18T11:20:00Z',
    lastTestAt: '2024-12-24T16:45:00Z',
    testStatus: 'success',
    creator: 'admin'
  },
  {
    id: '3',
    name: '业务通知Webhook',
    type: 'webhook',
    description: '业务系统状态通知',
    status: 'active',
    config: {
      url: 'https://business-system.example.com/notifications',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${token}',
        'X-Source': 'rayin-push'
      },
      template: '{"type": "${type}", "data": ${data}, "timestamp": "${now}"}'
    },
    createdTime: '2024-02-01T14:00:00Z',
    updatedTime: '2024-12-15T09:40:00Z',
    lastTestAt: '2024-12-23T13:20:00Z',
    testStatus: 'success',
    creator: 'zhang_wei'
  },
  {
    id: '4',
    name: '测试环境通知',
    type: 'webhook',
    description: '测试环境专用通知渠道',
    status: 'inactive',
    config: {
      url: 'https://test-env.example.com/webhook',
      method: 'GET',
      template: 'message=${message}&level=${level}&time=${now}'
    },
    createdTime: '2024-03-01T11:15:00Z',
    updatedTime: '2024-10-20T14:50:00Z',
    lastTestAt: '2024-11-15T10:30:00Z',
    testStatus: 'failed',
    creator: 'li_ming'
  },
  {
    id: '5',
    name: '管理员专用通知',
    type: 'wechat',
    description: '重要事件管理员通知',
    status: 'active',
    config: {
      url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx-admin-only',
      template: '🔔 **管理员通知**\n${content}\n\n详情: ${details}\n时间: ${now}'
    },
    createdTime: '2024-01-25T16:30:00Z',
    updatedTime: '2024-12-12T08:15:00Z',
    lastTestAt: '2024-12-22T17:00:00Z',
    testStatus: 'success',
    creator: 'admin'
  },
  {
    id: '6',
    name: '销售团队钉钉群',
    type: 'webhook',
    description: '销售业绩和客户通知',
    status: 'active',
    config: {
      url: 'https://oapi.dingtalk.com/robot/send?access_token=xxx-sales',
      template: '📊 **销售通知**\n${content}\n\n业绩: ${performance}\n时间: ${now}'
    },
    createdTime: '2024-02-10T10:00:00Z',
    updatedTime: '2024-12-10T12:00:00Z',
    lastTestAt: '2024-12-20T14:30:00Z',
    testStatus: 'success',
    creator: 'sales_manager'
  },
  {
    id: '7',
    name: '产品需求通知群',
    type: 'feishu',
    description: '产品需求变更和进度通知',
    status: 'active',
    config: {
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/xxx-product',
      template: '🚀 **产品通知**\n需求: ${requirement}\n进度: ${progress}\n时间: ${now}'
    },
    createdTime: '2024-02-15T11:30:00Z',
    updatedTime: '2024-12-08T15:45:00Z',
    lastTestAt: '2024-12-19T09:15:00Z',
    testStatus: 'success',
    creator: 'product_owner'
  },
  {
    id: '8',
    name: '财务审批通知',
    type: 'wechat',
    description: '财务审批流程通知',
    status: 'active',
    config: {
      url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx-finance',
      template: '💰 **财务通知**\n审批类型: ${type}\n金额: ${amount}\n申请人: ${applicant}\n时间: ${now}'
    },
    createdTime: '2024-02-20T13:20:00Z',
    updatedTime: '2024-12-05T16:30:00Z',
    lastTestAt: '2024-12-18T11:40:00Z',
    testStatus: 'success',
    creator: 'finance_head'
  },
  {
    id: '9',
    name: '人事招聘通知',
    type: 'webhook',
    description: '招聘进度和面试安排通知',
    status: 'active',
    config: {
      url: 'https://hr-system.example.com/webhook/recruitment',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Department': 'HR'
      },
      template: '👥 **人事通知**\n职位: ${position}\n候选人: ${candidate}\n面试时间: ${interview_time}'
    },
    createdTime: '2024-03-01T09:45:00Z',
    updatedTime: '2024-12-03T14:20:00Z',
    lastTestAt: '2024-12-17T10:25:00Z',
    testStatus: 'success',
    creator: 'hr_manager'
  },
  {
    id: '10',
    name: '客服工单系统',
    type: 'feishu',
    description: '客服工单状态变更通知',
    status: 'active',
    config: {
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/xxx-customer-service',
      template: '🎧 **客服通知**\n工单号: ${ticket_id}\n状态: ${status}\n客户: ${customer}\n时间: ${now}'
    },
    createdTime: '2024-03-05T15:10:00Z',
    updatedTime: '2024-12-01T17:55:00Z',
    lastTestAt: '2024-12-16T13:10:00Z',
    testStatus: 'success',
    creator: 'cs_lead'
  },
  {
    id: '11',
    name: '安全事件告警',
    type: 'wechat',
    description: '安全漏洞和攻击事件告警',
    status: 'active',
    config: {
      url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx-security',
      template: '🔒 **安全告警**\n事件类型: ${event_type}\n风险级别: ${risk_level}\n来源IP: ${source_ip}\n时间: ${now}'
    },
    createdTime: '2024-03-10T12:00:00Z',
    updatedTime: '2024-11-28T18:30:00Z',
    lastTestAt: '2024-12-15T16:45:00Z',
    testStatus: 'success',
    creator: 'security_admin'
  },
  {
    id: '12',
    name: '数据备份通知',
    type: 'webhook',
    description: '数据库备份状态通知',
    status: 'active',
    config: {
      url: 'https://backup-monitor.example.com/notify',
      method: 'POST',
      template: '💾 **备份通知**\n数据库: ${database}\n备份状态: ${status}\n文件大小: ${file_size}\n耗时: ${duration}'
    },
    createdTime: '2024-03-15T08:30:00Z',
    updatedTime: '2024-11-25T20:15:00Z',
    lastTestAt: '2024-12-14T07:20:00Z',
    testStatus: 'success',
    creator: 'dba'
  }
]